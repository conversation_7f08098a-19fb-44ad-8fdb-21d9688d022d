/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
	"/": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Health check response. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
							version: string;
							health: {
								status: string;
								database: {
									connected: boolean;
									error?: string;
								};
							};
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/stripe/webhook": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Webhook received successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							received: boolean;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/beacon": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						/** Format: uuid */
						uuid: string;
						type: string;
						/** Format: date-time */
						timestamp: string;
					};
				};
			};
			responses: {
				/** @description Beacon data received successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
							message: string;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/user/me": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description User response object. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							user: {
								id: string;
								email: string;
								name: string | null;
							};
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description User deleted successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description User not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						name?: string;
						/** Format: email */
						email?: string;
					};
				};
			};
			responses: {
				/** @description User updated successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							user: {
								id: string;
								email: string;
								name: string | null;
							};
							message: string;
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description User not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		trace?: never;
	};
	"/user/me/passkeys/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Passkey deleted successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/user/password": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						currentPassword: string;
						newPassword: string;
					};
				};
			};
			responses: {
				/** @description Password updated successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Unauthorized or incorrect current password. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description User not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/logs": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: {
					apiKeyId?: string;
					providerKeyId?: string;
					projectId?: string;
					orgId?: string;
					startDate?: string;
					endDate?: string;
					finishReason?: string;
					unifiedFinishReason?: string;
					provider?: string;
					model?: string;
					cursor?: string;
					orderBy?: "createdAt_asc" | "createdAt_desc";
					limit?: string;
				};
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description User logs response with pagination. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							/** @description Optional message about the response */
							message?: string;
							/** @description Array of log entries */
							logs: {
								id: string;
								requestId: string;
								createdAt: string;
								updatedAt: string;
								organizationId: string;
								projectId: string;
								apiKeyId: string;
								duration: number;
								requestedModel: string;
								requestedProvider: string | null;
								usedModel: string;
								usedProvider: string;
								responseSize: number;
								content: string | null;
								unifiedFinishReason: string | null;
								finishReason: string | null;
								promptTokens: string | null;
								completionTokens: string | null;
								totalTokens: string | null;
								reasoningTokens: string | null;
								messages?: unknown;
								temperature: number | null;
								maxTokens: number | null;
								topP: number | null;
								frequencyPenalty: number | null;
								presencePenalty: number | null;
								hasError: boolean | null;
								errorDetails: {
									statusCode: number;
									statusText: string;
									responseText: string;
								} | null;
								cost: number | null;
								inputCost: number | null;
								outputCost: number | null;
								requestCost: number | null;
								estimatedCost: boolean | null;
								canceled: boolean | null;
								streamed: boolean | null;
								cached: boolean | null;
								/** @enum {string} */
								mode: "api-keys" | "credits" | "hybrid";
								/** @enum {string} */
								usedMode: "api-keys" | "credits";
							}[];
							/** @description Pagination metadata */
							pagination: {
								/** @description Cursor to use for the next page of results, null if no more results */
								nextCursor: string | null;
								/** @description Whether there are more results available */
								hasMore: boolean;
								/** @description Number of items requested per page */
								limit: number;
							};
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/activity": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query: {
					days: string;
					projectId?: string;
				};
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Activity data grouped by day */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							activity: {
								date: string;
								requestCount: number;
								inputTokens: number;
								outputTokens: number;
								totalTokens: number;
								cost: number;
								inputCost: number;
								outputCost: number;
								requestCost: number;
								errorCount: number;
								errorRate: number;
								cacheCount: number;
								cacheRate: number;
								modelBreakdown: {
									model: string;
									provider: string;
									requestCount: number;
									inputTokens: number;
									outputTokens: number;
									totalTokens: number;
									cost: number;
								}[];
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/keys/api": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: {
					projectId?: string;
				};
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description List of API keys. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							apiKeys: {
								id: string;
								createdAt: string;
								updatedAt: string;
								description: string;
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								projectId: string;
								maskedToken: string;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						description: string;
						projectId: string;
					};
				};
			};
			responses: {
				/** @description API key created successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							apiKey: {
								id: string;
								createdAt: string;
								updatedAt: string;
								description: string;
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								projectId: string;
								token: string;
							};
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/keys/api/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description API key deleted successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description API key not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						/** @enum {string} */
						status: "active" | "inactive";
					};
				};
			};
			responses: {
				/** @description API key status updated successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
							apiKey: {
								id: string;
								createdAt: string;
								updatedAt: string;
								description: string;
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								projectId: string;
								maskedToken: string;
							};
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description API key not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		trace?: never;
	};
	"/keys/provider": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description List of provider keys. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							providerKeys: {
								id: string;
								createdAt: string;
								updatedAt: string;
								provider: string;
								baseUrl: string | null;
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								organizationId: string;
								maskedToken: string;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						provider: string;
						token: string;
						/** Format: uri */
						baseUrl?: string;
						organizationId: string;
					};
				};
			};
			responses: {
				/** @description Provider key created successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							providerKey: {
								id: string;
								createdAt: string;
								updatedAt: string;
								provider: string;
								baseUrl: string | null;
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								organizationId: string;
								token: string;
							};
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/keys/provider/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Provider key deleted successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Provider key not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						/** @enum {string} */
						status: "active" | "inactive";
					};
				};
			};
			responses: {
				/** @description Provider key status updated successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
							providerKey: {
								id: string;
								createdAt: string;
								updatedAt: string;
								provider: string;
								baseUrl: string | null;
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								organizationId: string;
								maskedToken: string;
							};
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Provider key not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		trace?: never;
	};
	"/projects/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Project deleted successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Project not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						cachingEnabled?: boolean;
						cacheDurationSeconds?: number;
						/** @enum {string} */
						mode?: "api-keys" | "credits" | "hybrid";
					};
				};
			};
			responses: {
				/** @description Project settings updated successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
							project: {
								id: string;
								createdAt: string;
								updatedAt: string;
								name: string;
								organizationId: string;
								cachingEnabled: boolean;
								cacheDurationSeconds: number;
								/** @enum {string} */
								mode: "api-keys" | "credits" | "hybrid";
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
							};
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Project not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		trace?: never;
	};
	"/projects": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						name: string;
						organizationId: string;
						cachingEnabled?: boolean;
						cacheDurationSeconds?: number;
						/** @enum {string} */
						mode?: "api-keys" | "credits" | "hybrid";
					};
				};
			};
			responses: {
				/** @description Project created successfully. */
				201: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							project: {
								id: string;
								createdAt: string;
								updatedAt: string;
								name: string;
								organizationId: string;
								cachingEnabled: boolean;
								cacheDurationSeconds: number;
								/** @enum {string} */
								mode: "api-keys" | "credits" | "hybrid";
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
							};
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description You do not have access to this organization. */
				403: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/orgs": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description List of organizations the user belongs to */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							organizations: {
								id: string;
								createdAt: string;
								updatedAt: string;
								name: string;
								credits: string;
								/** @enum {string} */
								plan: "free" | "pro";
								planExpiresAt: string | null;
								/** @enum {string} */
								retentionLevel: "retain" | "none";
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								autoTopUpEnabled: boolean;
								autoTopUpThreshold: string | null;
								autoTopUpAmount: string | null;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						name: string;
					};
				};
			};
			responses: {
				/** @description Organization created successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							organization: {
								id: string;
								createdAt: string;
								updatedAt: string;
								name: string;
								credits: string;
								/** @enum {string} */
								plan: "free" | "pro";
								planExpiresAt: string | null;
								/** @enum {string} */
								retentionLevel: "retain" | "none";
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								autoTopUpEnabled: boolean;
								autoTopUpThreshold: string | null;
								autoTopUpAmount: string | null;
							};
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/orgs/{id}/projects": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description List of projects for the specified organization */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							projects: {
								id: string;
								createdAt: string;
								updatedAt: string;
								name: string;
								organizationId: string;
								cachingEnabled: boolean;
								cacheDurationSeconds: number;
								/** @enum {string} */
								mode: "api-keys" | "credits" | "hybrid";
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/orgs/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Organization deleted successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Organization not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						name?: string;
						/** @enum {string} */
						retentionLevel?: "retain" | "none";
						autoTopUpEnabled?: boolean;
						autoTopUpThreshold?: number;
						autoTopUpAmount?: number;
					};
				};
			};
			responses: {
				/** @description Organization updated successfully. */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
							organization: {
								id: string;
								createdAt: string;
								updatedAt: string;
								name: string;
								credits: string;
								/** @enum {string} */
								plan: "free" | "pro";
								planExpiresAt: string | null;
								/** @enum {string} */
								retentionLevel: "retain" | "none";
								/** @enum {string|null} */
								status: "active" | "inactive" | "deleted" | null;
								autoTopUpEnabled: boolean;
								autoTopUpThreshold: string | null;
								autoTopUpAmount: string | null;
							};
						};
					};
				};
				/** @description Unauthorized. */
				401: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
				/** @description Organization not found. */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		trace?: never;
	};
	"/orgs/{id}/transactions": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description List of transactions for the specified organization */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							transactions: {
								id: string;
								createdAt: string;
								updatedAt: string;
								organizationId: string;
								/** @enum {string} */
								type:
									| "subscription_start"
									| "subscription_cancel"
									| "subscription_end"
									| "credit_topup";
								amount: string | null;
								creditAmount: string | null;
								currency: string;
								/** @enum {string} */
								status: "pending" | "completed" | "failed";
								stripePaymentIntentId: string | null;
								stripeInvoiceId: string | null;
								description: string | null;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/create-payment-intent": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						amount: number;
					};
				};
			};
			responses: {
				/** @description Payment intent created successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							clientSecret: string;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/create-setup-intent": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Setup intent created successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							clientSecret: string;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/payment-methods": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Payment methods retrieved successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							paymentMethods: {
								id: string;
								stripePaymentMethodId: string;
								type: string;
								isDefault: boolean;
								cardBrand?: string;
								cardLast4?: string;
								expiryMonth?: number;
								expiryYear?: number;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/payment-methods/default": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						paymentMethodId: string;
					};
				};
			};
			responses: {
				/** @description Default payment method set successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/payment-methods/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Payment method deleted successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/top-up-with-saved-method": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						amount: number;
						paymentMethodId: string;
					};
				};
			};
			responses: {
				/** @description Payment processed successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/payments/calculate-fees": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						amount: number;
						paymentMethodId?: string;
					};
				};
			};
			responses: {
				/** @description Fee calculation completed successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							baseAmount: number;
							stripeFee: number;
							internationalFee: number;
							planFee: number;
							totalFees: number;
							totalAmount: number;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/chat/completion": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						messages: {
							/** @enum {string} */
							role: "user" | "assistant" | "system";
							content: string;
						}[];
						model: string;
						/** @default false */
						stream?: boolean;
						apiKey?: string;
					};
				};
			};
			responses: {
				/** @description Chat completion response */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content?: never;
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/chats": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description List of user's chats */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							chats: {
								id: string;
								title: string;
								model: string;
								/** @enum {string} */
								status: "active" | "archived" | "deleted";
								/** Format: date-time */
								createdAt: string;
								/** Format: date-time */
								updatedAt: string;
								messageCount: number;
							}[];
						};
					};
				};
			};
		};
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						title: string;
						model: string;
					};
				};
			};
			responses: {
				/** @description Chat created successfully */
				201: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							chat: {
								id: string;
								title: string;
								model: string;
								/** @enum {string} */
								status: "active" | "archived" | "deleted";
								/** Format: date-time */
								createdAt: string;
								/** Format: date-time */
								updatedAt: string;
								messageCount: number;
							};
						};
					};
				};
				/** @description Chat limit reached or validation error */
				400: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/chats/{id}": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Chat with messages */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							chat: {
								id: string;
								title: string;
								model: string;
								/** @enum {string} */
								status: "active" | "archived" | "deleted";
								/** Format: date-time */
								createdAt: string;
								/** Format: date-time */
								updatedAt: string;
								messageCount: number;
							};
							messages: {
								id: string;
								/** @enum {string} */
								role: "user" | "assistant" | "system";
								content: string;
								sequence: number;
								/** Format: date-time */
								createdAt: string;
							}[];
						};
					};
				};
				/** @description Chat not found */
				404: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Chat deleted successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: string;
						};
					};
				};
			};
		};
		options?: never;
		head?: never;
		patch: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						title?: string;
						/** @enum {string} */
						status?: "active" | "archived";
					};
				};
			};
			responses: {
				/** @description Chat updated successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							chat: {
								id: string;
								title: string;
								model: string;
								/** @enum {string} */
								status: "active" | "archived" | "deleted";
								/** Format: date-time */
								createdAt: string;
								/** Format: date-time */
								updatedAt: string;
								messageCount: number;
							};
						};
					};
				};
			};
		};
		trace?: never;
	};
	"/chats/{id}/messages": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path: {
					id: string;
				};
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						/** @enum {string} */
						role: "user" | "assistant" | "system";
						content: string;
					};
				};
			};
			responses: {
				/** @description Message added successfully */
				201: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							message: {
								id: string;
								/** @enum {string} */
								role: "user" | "assistant" | "system";
								content: string;
								sequence: number;
								/** Format: date-time */
								createdAt: string;
							};
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/subscriptions/create-pro-subscription": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: {
				content: {
					"application/json": {
						/**
						 * @default monthly
						 * @enum {string}
						 */
						billingCycle?: "monthly" | "yearly";
					};
				};
			};
			responses: {
				/** @description Stripe Checkout session created successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							checkoutUrl: string;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/subscriptions/cancel-pro-subscription": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Pro subscription canceled successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/subscriptions/resume-pro-subscription": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Pro subscription resumed successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/subscriptions/upgrade-to-yearly": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get?: never;
		put?: never;
		post: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Subscription upgraded to yearly successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							success: boolean;
						};
					};
				};
			};
		};
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
	"/subscriptions/status": {
		parameters: {
			query?: never;
			header?: never;
			path?: never;
			cookie?: never;
		};
		get: {
			parameters: {
				query?: never;
				header?: never;
				path?: never;
				cookie?: never;
			};
			requestBody?: never;
			responses: {
				/** @description Subscription status retrieved successfully */
				200: {
					headers: {
						[name: string]: unknown;
					};
					content: {
						"application/json": {
							/** @enum {string} */
							plan: "free" | "pro";
							subscriptionId: string | null;
							planExpiresAt: string | null;
							subscriptionCancelled: boolean;
							/** @enum {string|null} */
							billingCycle: "monthly" | "yearly" | null;
						};
					};
				};
			};
		};
		put?: never;
		post?: never;
		delete?: never;
		options?: never;
		head?: never;
		patch?: never;
		trace?: never;
	};
}
export type webhooks = Record<string, never>;
export interface components {
	schemas: never;
	responses: never;
	parameters: never;
	requestBodies: never;
	headers: never;
	pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;
