import { describe, expect, it, vi } from "vitest";

import { app } from "../index";
import { posthog } from "../posthog";

// Mock PostHog
vi.mock("../posthog", () => ({
	posthog: {
		capture: vi.fn(),
	},
}));

describe("beacon endpoint", () => {
	it("should not send data to PostHog when telemetry is disabled", async () => {
		// Set TELEMETRY_ACTIVE to false
		const originalValue = process.env.TELEMETRY_ACTIVE;
		process.env.TELEMETRY_ACTIVE = "false";

		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			type: "self-host",
			timestamp: "2024-01-01T00:00:00.000Z",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(200);
		const responseData = await response.json();
		expect(responseData).toEqual({
			success: true,
			message: "Beacon received (telemetry disabled)",
		});

		// Verify PostHog was NOT called when telemetry is disabled
		expect(posthog.capture).not.toHaveBeenCalled();

		// Restore original value
		process.env.TELEMETRY_ACTIVE = originalValue;
	});

	it("should accept valid beacon data", async () => {
		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			type: "self-host",
			timestamp: "2024-01-01T00:00:00.000Z",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(200);
		const responseData = await response.json();
		expect(responseData).toEqual({
			success: true,
			message: "Beacon received successfully",
		});

		// Verify PostHog was called with correct data including new tracking fields
		expect(posthog.capture).toHaveBeenCalledWith({
			distinctId: beaconData.uuid,
			event: "self_hosted_installation_beacon",
			properties: {
				installation: beaconData.type,
				timestamp: beaconData.timestamp,
				source: "self_hosted_api",
				version: process.env.APP_VERSION || "v0.0.0-unknown",
				client_ip: null, // No IP headers provided
				country: undefined,
				region: undefined,
				cloud_provider: "unknown",
			},
		});
	});

	it("should extract Cloudflare headers correctly", async () => {
		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			type: "self-host",
			timestamp: "2024-01-01T00:00:00.000Z",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"CF-Connecting-IP": "************",
				"CF-IPCountry": "US",
				"CF-Region": "California",
				"CF-Ray": "8a1b2c3d4e5f6789-SJC",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(200);

		// Verify PostHog was called with Cloudflare data
		expect(posthog.capture).toHaveBeenCalledWith({
			distinctId: beaconData.uuid,
			event: "self_hosted_installation_beacon",
			properties: {
				installation: beaconData.type,
				timestamp: beaconData.timestamp,
				source: "self_hosted_api",
				version: process.env.APP_VERSION || "v0.0.0-unknown",
				client_ip: "************",
				country: "US",
				region: "California",
				cloud_provider: "cloudflare",
			},
		});
	});

	it("should extract GCP headers correctly", async () => {
		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			type: "self-host",
			timestamp: "2024-01-01T00:00:00.000Z",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Forwarded-For": "*************, ***********",
				"X-Google-Cloud-Region": "us-central1",
				"X-Cloud-Trace-Context": "105445aa7843bc8bf206b120001000/1;o=1",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(200);

		// Verify PostHog was called with GCP data
		expect(posthog.capture).toHaveBeenCalledWith({
			distinctId: beaconData.uuid,
			event: "self_hosted_installation_beacon",
			properties: {
				installation: beaconData.type,
				timestamp: beaconData.timestamp,
				source: "self_hosted_api",
				version: process.env.APP_VERSION || "v0.0.0-unknown",
				client_ip: "*************", // First IP from X-Forwarded-For
				country: undefined, // GCP doesn't provide country in standard headers
				region: "us-central1",
				cloud_provider: "gcp",
			},
		});
	});

	it("should handle X-Real-IP fallback", async () => {
		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			type: "self-host",
			timestamp: "2024-01-01T00:00:00.000Z",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Real-IP": "***********",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(200);

		// Verify PostHog was called with X-Real-IP data
		expect(posthog.capture).toHaveBeenCalledWith({
			distinctId: beaconData.uuid,
			event: "self_hosted_installation_beacon",
			properties: {
				installation: beaconData.type,
				timestamp: beaconData.timestamp,
				source: "self_hosted_api",
				version: process.env.APP_VERSION || "v0.0.0-unknown",
				client_ip: "***********",
				country: undefined,
				region: undefined,
				cloud_provider: "unknown",
			},
		});
	});

	it("should reject invalid UUID", async () => {
		const beaconData = {
			uuid: "invalid-uuid",
			type: "self-host",
			timestamp: "2024-01-01T00:00:00.000Z",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(400);
	});

	it("should reject invalid timestamp", async () => {
		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			type: "self-host",
			timestamp: "invalid-timestamp",
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(400);
	});

	it("should reject missing fields", async () => {
		const beaconData = {
			uuid: "123e4567-e89b-12d3-a456-************",
			// missing type and timestamp
		};

		const response = await app.request("/beacon", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(beaconData),
		});

		expect(response.status).toBe(400);
	});
});
