{"id": "7bdf1223-c188-457f-ac7c-b03a617b441e", "prevId": "47370bc3-28ab-427e-8f8d-94bcd8813d9c", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_key": {"name": "api_key", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"api_key_project_id_project_id_fk": {"name": "api_key_project_id_project_id_fk", "tableFrom": "api_key", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_key_token_unique": {"name": "api_key_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}}, "indexes": {}, "foreignKeys": {"chat_user_id_user_id_fk": {"name": "chat_user_id_user_id_fk", "tableFrom": "chat", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.installation": {"name": "installation", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "uuid": {"name": "uuid", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"installation_uuid_unique": {"name": "installation_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lock": {"name": "lock", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lock_key_unique": {"name": "lock_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.log": {"name": "log", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "request_id": {"name": "request_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "api_key_id": {"name": "api_key_id", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "requested_model": {"name": "requested_model", "type": "text", "primaryKey": false, "notNull": true}, "requested_provider": {"name": "requested_provider", "type": "text", "primaryKey": false, "notNull": false}, "used_model": {"name": "used_model", "type": "text", "primaryKey": false, "notNull": true}, "used_provider": {"name": "used_provider", "type": "text", "primaryKey": false, "notNull": true}, "response_size": {"name": "response_size", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "finish_reason": {"name": "finish_reason", "type": "text", "primaryKey": false, "notNull": false}, "unified_finish_reason": {"name": "unified_finish_reason", "type": "text", "primaryKey": false, "notNull": false}, "prompt_tokens": {"name": "prompt_tokens", "type": "numeric", "primaryKey": false, "notNull": false}, "completion_tokens": {"name": "completion_tokens", "type": "numeric", "primaryKey": false, "notNull": false}, "total_tokens": {"name": "total_tokens", "type": "numeric", "primaryKey": false, "notNull": false}, "reasoning_tokens": {"name": "reasoning_tokens", "type": "numeric", "primaryKey": false, "notNull": false}, "messages": {"name": "messages", "type": "json", "primaryKey": false, "notNull": false}, "temperature": {"name": "temperature", "type": "real", "primaryKey": false, "notNull": false}, "max_tokens": {"name": "max_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "top_p": {"name": "top_p", "type": "real", "primaryKey": false, "notNull": false}, "frequency_penalty": {"name": "frequency_penalty", "type": "real", "primaryKey": false, "notNull": false}, "presence_penalty": {"name": "presence_penalty", "type": "real", "primaryKey": false, "notNull": false}, "has_error": {"name": "has_error", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "error_details": {"name": "error_details", "type": "json", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "real", "primaryKey": false, "notNull": false}, "input_cost": {"name": "input_cost", "type": "real", "primaryKey": false, "notNull": false}, "output_cost": {"name": "output_cost", "type": "real", "primaryKey": false, "notNull": false}, "request_cost": {"name": "request_cost", "type": "real", "primaryKey": false, "notNull": false}, "estimated_cost": {"name": "estimated_cost", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "canceled": {"name": "canceled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "streamed": {"name": "streamed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cached": {"name": "cached", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "mode": {"name": "mode", "type": "text", "primaryKey": false, "notNull": true}, "used_mode": {"name": "used_mode", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"log_organization_id_organization_id_fk": {"name": "log_organization_id_organization_id_fk", "tableFrom": "log", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "log_project_id_project_id_fk": {"name": "log_project_id_project_id_fk", "tableFrom": "log", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "log_api_key_id_api_key_id_fk": {"name": "log_api_key_id_api_key_id_fk", "tableFrom": "log", "tableTo": "api_key", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.message": {"name": "message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "chat_id": {"name": "chat_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"message_chat_id_chat_id_fk": {"name": "message_chat_id_chat_id_fk", "tableFrom": "message", "tableTo": "chat", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "numeric", "primaryKey": false, "notNull": true, "default": "'0'"}, "auto_top_up_enabled": {"name": "auto_top_up_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "auto_top_up_threshold": {"name": "auto_top_up_threshold", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'10'"}, "auto_top_up_amount": {"name": "auto_top_up_amount", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'10'"}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true, "default": "'free'"}, "plan_expires_at": {"name": "plan_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "subscription_cancelled": {"name": "subscription_cancelled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "retention_level": {"name": "retention_level", "type": "text", "primaryKey": false, "notNull": true, "default": "'retain'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_stripeCustomerId_unique": {"name": "organization_stripeCustomerId_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}, "organization_stripeSubscriptionId_unique": {"name": "organization_stripeSubscriptionId_unique", "nullsNotDistinct": false, "columns": ["stripe_subscription_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_action": {"name": "organization_action", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organization_action_organization_id_organization_id_fk": {"name": "organization_action_organization_id_organization_id_fk", "tableFrom": "organization_action", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.passkey": {"name": "passkey", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "public_key": {"name": "public_key", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "credential_id": {"name": "credential_id", "type": "text", "primaryKey": false, "notNull": true}, "counter": {"name": "counter", "type": "integer", "primaryKey": false, "notNull": true}, "device_type": {"name": "device_type", "type": "text", "primaryKey": false, "notNull": false}, "backed_up": {"name": "backed_up", "type": "boolean", "primaryKey": false, "notNull": false}, "transports": {"name": "transports", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"passkey_user_id_user_id_fk": {"name": "passkey_user_id_user_id_fk", "tableFrom": "passkey", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_method": {"name": "payment_method", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "stripe_payment_method_id": {"name": "stripe_payment_method_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"payment_method_organization_id_organization_id_fk": {"name": "payment_method_organization_id_organization_id_fk", "tableFrom": "payment_method", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "caching_enabled": {"name": "caching_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "cache_duration_seconds": {"name": "cache_duration_seconds", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "mode": {"name": "mode", "type": "text", "primaryKey": false, "notNull": true, "default": "'credits'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}}, "indexes": {}, "foreignKeys": {"project_organization_id_organization_id_fk": {"name": "project_organization_id_organization_id_fk", "tableFrom": "project", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.provider_key": {"name": "provider_key", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "base_url": {"name": "base_url", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"provider_key_organization_id_organization_id_fk": {"name": "provider_key_organization_id_organization_id_fk", "tableFrom": "provider_key", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transaction": {"name": "transaction", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric", "primaryKey": false, "notNull": false}, "credit_amount": {"name": "credit_amount", "type": "numeric", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'completed'"}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_invoice_id": {"name": "stripe_invoice_id", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"transaction_organization_id_organization_id_fk": {"name": "transaction_organization_id_organization_id_fk", "tableFrom": "transaction", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_organization": {"name": "user_organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_organization_user_id_user_id_fk": {"name": "user_organization_user_id_user_id_fk", "tableFrom": "user_organization", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_organization_organization_id_organization_id_fk": {"name": "user_organization_organization_id_organization_id_fk", "tableFrom": "user_organization", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}